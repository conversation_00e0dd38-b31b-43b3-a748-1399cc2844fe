"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ProtocolHandler_1 = require("../../src/server/core/ProtocolHandler");
describe('ProtocolHandler', () => {
    let handler;
    beforeEach(() => {
        handler = new ProtocolHandler_1.ProtocolHandler();
    });
    describe('getCapabilities', () => {
        it('should return server capabilities', () => {
            const capabilities = handler.getCapabilities();
            expect(capabilities).toHaveProperty('tools');
            expect(capabilities).toHaveProperty('resources');
            expect(capabilities).toHaveProperty('prompts');
        });
    });
    describe('handleRequest', () => {
        it('should return capabilities for mcp.getCapabilities', () => {
            const response = handler.handleRequest({ method: 'mcp.getCapabilities', params: {} });
            expect(response).toEqual({ capabilities: handler.getCapabilities() });
        });
        it('should handle listPrompts method', () => {
            const listResponse = handler.handleRequest({ method: 'mcp.listPrompts', params: {} });
            expect(listResponse).toHaveProperty('success', true);
            expect(listResponse).toHaveProperty('data');
            expect(Array.isArray(listResponse.data)).toBe(true);
        });
        it('should handle searchPrompts method', () => {
            const searchResponse = handler.handleRequest({ method: 'mcp.searchPrompts', params: { query: 'test' } });
            expect(searchResponse).toHaveProperty('success', true);
            expect(searchResponse).toHaveProperty('data');
            expect(Array.isArray(searchResponse.data)).toBe(true);
        });
        it('should return error for invalid methods', () => {
            const response = handler.handleRequest({ method: 'invalid.method', params: {} });
            expect(response).toEqual({ error: 'Invalid MCP method' });
        });
    });
});
