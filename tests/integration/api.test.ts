import request from 'supertest';
import { Server } from '../../src/server/core/Server';

const server = new Server();
const app = server.getExpressApp();

describe('API Endpoints', () => {
  beforeAll(() => {
    server.start(3003);
  });

  afterAll(() => {
    server.stop();
  });

  describe('GET /', () => {
    it('should return server info', async () => {
      const response = await request(app).get('/');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('endpoints');
    });
  });

  describe('GET /prompts', () => {
    it('should list prompts', async () => {
      const response = await request(app).get('/prompts');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
    });
  });

  describe('GET /collections', () => {
    it('should list collections', async () => {
      const response = await request(app).get('/collections');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
    });
  });

  describe('GET /categories', () => {
    it('should list categories', async () => {
      const response = await request(app).get('/categories');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
    });
  });

  describe('POST /mcp', () => {
    it('should handle MCP requests', async () => {
      const token = 'test-auth-token'; // Mock token for testing
      const response = await request(app)
        .post('/mcp')
        .set('Authorization', `Bearer ${token}`)
        .send({ method: 'mcp.getCapabilities', params: {} });
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('capabilities');
    });
  });
});