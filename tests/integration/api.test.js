"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const Server_1 = require("../../src/server/core/Server");
const server = new Server_1.Server();
const app = server.getExpressApp();
describe('API Endpoints', () => {
    beforeAll(() => {
        server.start(3003);
    });
    afterAll(() => {
        server.stop();
    });
    describe('GET /', () => {
        it('should return server info', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield (0, supertest_1.default)(app).get('/');
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('version');
            expect(response.body).toHaveProperty('endpoints');
        }));
    });
    describe('GET /prompts', () => {
        it('should list prompts', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield (0, supertest_1.default)(app).get('/prompts');
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('success', true);
            expect(response.body).toHaveProperty('data');
        }));
    });
    describe('GET /collections', () => {
        it('should list collections', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield (0, supertest_1.default)(app).get('/collections');
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('success', true);
            expect(response.body).toHaveProperty('data');
        }));
    });
    describe('GET /categories', () => {
        it('should list categories', () => __awaiter(void 0, void 0, void 0, function* () {
            const response = yield (0, supertest_1.default)(app).get('/categories');
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('success', true);
            expect(response.body).toHaveProperty('data');
        }));
    });
    describe('POST /mcp', () => {
        it('should handle MCP requests', () => __awaiter(void 0, void 0, void 0, function* () {
            const token = 'test-auth-token'; // Mock token for testing
            const response = yield (0, supertest_1.default)(app)
                .post('/mcp')
                .set('Authorization', `Bearer ${token}`)
                .send({ method: 'mcp.getCapabilities', params: {} });
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('capabilities');
        }));
    });
});
