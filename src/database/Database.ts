import Database from 'better-sqlite3';
import { join } from 'path';
import { randomUUID } from 'crypto';
import { Prompt, Category, Collection } from '../types';

export class PromptDatabase {
  private db: Database.Database;

  constructor(dbPath: string = join(__dirname, '../../data/library.db')) {
    this.db = new Database(dbPath);
    this.initializeDatabase();
    this.initializeDefaultCategories();
  }

  private initializeDatabase(): void {
    // Create tables if they don't exist
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS prompts (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        content TEXT NOT NULL,
        category_id TEXT,
        tags TEXT,
        author TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        version INTEGER DEFAULT 1,
        is_private BOOLEAN DEFAULT FALSE,
        usage_count INTEGER DEFAULT 0,
        rating REAL DEFAULT 0,
        metadata TEXT
      );

      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        parent_id TEXT,
        icon TEXT,
        color TEXT,
        sort_order INTEGER DEFAULT 0,
        is_system BOOLEAN DEFAULT FALSE
      );

      CREATE TABLE IF NOT EXISTS collections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        prompt_ids TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        author_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE VIRTUAL TABLE IF NOT EXISTS prompts_fts USING fts5(
        title, description, content, tags,
        content='prompts',
        content_rowid='id'
      );

      -- Triggers to keep FTS table in sync
      CREATE TRIGGER IF NOT EXISTS prompts_ai AFTER INSERT ON prompts BEGIN
        INSERT INTO prompts_fts(rowid, title, description, content, tags)
        VALUES (new.rowid, new.title, new.description, new.content, new.tags);
      END;

      CREATE TRIGGER IF NOT EXISTS prompts_ad AFTER DELETE ON prompts BEGIN
        INSERT INTO prompts_fts(prompts_fts, rowid, title, description, content, tags)
        VALUES('delete', old.rowid, old.title, old.description, old.content, old.tags);
      END;

      CREATE TRIGGER IF NOT EXISTS prompts_au AFTER UPDATE ON prompts BEGIN
        INSERT INTO prompts_fts(prompts_fts, rowid, title, description, content, tags)
        VALUES('delete', old.rowid, old.title, old.description, old.content, old.tags);
        INSERT INTO prompts_fts(rowid, title, description, content, tags)
        VALUES (new.rowid, new.title, new.description, new.content, new.tags);
      END;
    `);
  }

  // Prompt CRUD operations
  createPrompt(prompt: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt' | 'version'>): Prompt {
    const id = randomUUID();
    const tagsJson = JSON.stringify(prompt.tags);
    const metadataJson = JSON.stringify(prompt.metadata);

    const stmt = this.db.prepare(
      'INSERT INTO prompts (id, title, description, content, category_id, tags, author, is_private, metadata) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
    );

    stmt.run(
      id,
      prompt.title,
      prompt.description,
      prompt.content,
      prompt.category,
      tagsJson,
      prompt.author,
      prompt.isPrivate,
      metadataJson
    );

    // Manually insert into FTS table for immediate availability
    const ftsStmt = this.db.prepare(
      'INSERT INTO prompts_fts(title, description, content, tags) VALUES (?, ?, ?, ?)'
    );
    ftsStmt.run(prompt.title, prompt.description, prompt.content, tagsJson);

    return this.getPrompt(id);
  }

  getPrompt(id: string): Prompt {
    const stmt = this.db.prepare('SELECT * FROM prompts WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) throw new Error('Prompt not found');
    
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      content: row.content,
      category: row.category_id,
      tags: JSON.parse(row.tags || '[]'),
      author: row.author,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      version: row.version,
      isPrivate: row.is_private,
      usageCount: row.usage_count,
      rating: row.rating,
      metadata: JSON.parse(row.metadata || '{}')
    };
  }

  updatePrompt(id: string, updates: Partial<Prompt>): Prompt {
    const current = this.getPrompt(id);
    const newVersion = current.version + 1;
    
    const stmt = this.db.prepare(
      'UPDATE prompts SET title = ?, description = ?, content = ?, category_id = ?, tags = ?, is_private = ?, metadata = ?, version = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?'
    );
    
    stmt.run(
      updates.title ?? current.title,
      updates.description ?? current.description,
      updates.content ?? current.content,
      updates.category ?? current.category,
      JSON.stringify(updates.tags ?? current.tags),
      updates.isPrivate ?? current.isPrivate,
      JSON.stringify(updates.metadata ?? current.metadata),
      newVersion,
      id
    );
    
    return this.getPrompt(id);
  }

  deletePrompt(id: string): void {
    const stmt = this.db.prepare('DELETE FROM prompts WHERE id = ?');
    stmt.run(id);
  }

  listPrompts(filter: { category?: string; author?: string; isPrivate?: boolean } = {}): Prompt[] {
    let query = 'SELECT * FROM prompts WHERE 1=1';
    const params: any[] = [];
    
    if (filter.category) {
      query += ' AND category_id = ?';
      params.push(filter.category);
    }
    
    if (filter.author) {
      query += ' AND author = ?';
      params.push(filter.author);
    }
    
    if (filter.isPrivate !== undefined) {
      query += ' AND is_private = ?';
      params.push(filter.isPrivate);
    }
    
    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as any[];
    
    return rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      content: row.content,
      category: row.category_id,
      tags: JSON.parse(row.tags || '[]'),
      author: row.author,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      version: row.version,
      isPrivate: row.is_private,
      usageCount: row.usage_count,
      rating: row.rating,
      metadata: JSON.parse(row.metadata || '{}')
    }));
  }

  searchPrompts(query: string): Prompt[] {
    if (!query || query.trim().length < 2) {
      return [];
    }

    // Escape special FTS characters and prepare query
    const escapedQuery = query.replace(/['"]/g, '').trim();
    const ftsQuery = `"${escapedQuery}"*`;

    try {
      const stmt = this.db.prepare(`
        SELECT p.* FROM prompts p
        JOIN prompts_fts fts ON p.rowid = fts.rowid
        WHERE prompts_fts MATCH ?
        ORDER BY bm25(prompts_fts)
        LIMIT 50
      `);

      const rows = stmt.all(ftsQuery) as any[];
      return rows.map(row => ({
        id: row.id,
        title: row.title,
        description: row.description,
        content: row.content,
        category: row.category_id,
        tags: JSON.parse(row.tags || '[]'),
        author: row.author,
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
        version: row.version,
        isPrivate: row.is_private,
        usageCount: row.usage_count,
        rating: row.rating,
        metadata: JSON.parse(row.metadata || '{}')
      }));
    } catch (error) {
      // Fallback to simple LIKE search if FTS fails
      console.warn('FTS search failed, falling back to LIKE search:', error);
      const likeQuery = `%${escapedQuery}%`;
      const stmt = this.db.prepare(`
        SELECT * FROM prompts
        WHERE title LIKE ? OR description LIKE ? OR content LIKE ?
        ORDER BY updated_at DESC
        LIMIT 50
      `);

      const rows = stmt.all(likeQuery, likeQuery, likeQuery) as any[];
      return rows.map(row => ({
        id: row.id,
        title: row.title,
        description: row.description,
        content: row.content,
        category: row.category_id,
        tags: JSON.parse(row.tags || '[]'),
        author: row.author,
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at),
        version: row.version,
        isPrivate: row.is_private,
        usageCount: row.usage_count,
        rating: row.rating,
        metadata: JSON.parse(row.metadata || '{}')
      }));
    }
  }

  // Category CRUD operations
  createCategory(category: Omit<Category, 'id'>): Category {
    const id = randomUUID();
    const stmt = this.db.prepare(
      'INSERT INTO categories (id, name, description, parent_id, icon, color, sort_order, is_system) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
    );

    stmt.run(
      id,
      category.name,
      category.description,
      category.parentId,
      category.icon,
      category.color,
      category.sortOrder,
      category.isSystem
    );

    return this.getCategory(id);
  }

  getCategory(id: string): Category {
    const stmt = this.db.prepare('SELECT * FROM categories WHERE id = ?');
    const row = stmt.get(id) as any;

    if (!row) throw new Error('Category not found');

    return {
      id: row.id,
      name: row.name,
      description: row.description,
      parentId: row.parent_id,
      icon: row.icon,
      color: row.color,
      sortOrder: row.sort_order,
      isSystem: row.is_system
    };
  }

  updateCategory(id: string, updates: Partial<Category>): Category {
    const current = this.getCategory(id);

    const stmt = this.db.prepare(
      'UPDATE categories SET name = ?, description = ?, parent_id = ?, icon = ?, color = ?, sort_order = ?, is_system = ? WHERE id = ?'
    );

    stmt.run(
      updates.name ?? current.name,
      updates.description ?? current.description,
      updates.parentId ?? current.parentId,
      updates.icon ?? current.icon,
      updates.color ?? current.color,
      updates.sortOrder ?? current.sortOrder,
      updates.isSystem ?? current.isSystem,
      id
    );

    return this.getCategory(id);
  }

  deleteCategory(id: string): void {
    const stmt = this.db.prepare('DELETE FROM categories WHERE id = ?');
    stmt.run(id);
  }

  listCategories(): Category[] {
    const stmt = this.db.prepare('SELECT * FROM categories ORDER BY sort_order, name');
    const rows = stmt.all() as any[];

    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      parentId: row.parent_id,
      icon: row.icon,
      color: row.color,
      sortOrder: row.sort_order,
      isSystem: row.is_system
    }));
  }

  // Collection CRUD operations
  createCollection(collection: Omit<Collection, 'id' | 'createdAt' | 'updatedAt'>): Collection {
    const id = randomUUID();
    const stmt = this.db.prepare(
      'INSERT INTO collections (id, name, description, prompt_ids, is_public, author_id) VALUES (?, ?, ?, ?, ?, ?)'
    );

    stmt.run(
      id,
      collection.name,
      collection.description,
      JSON.stringify(collection.promptIds),
      collection.isPublic,
      collection.authorId
    );

    return this.getCollection(id);
  }

  getCollection(id: string): Collection {
    const stmt = this.db.prepare('SELECT * FROM collections WHERE id = ?');
    const row = stmt.get(id) as any;

    if (!row) throw new Error('Collection not found');

    return {
      id: row.id,
      name: row.name,
      description: row.description,
      promptIds: JSON.parse(row.prompt_ids || '[]'),
      isPublic: row.is_public,
      authorId: row.author_id,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  updateCollection(id: string, updates: Partial<Collection>): Collection {
    const current = this.getCollection(id);

    const stmt = this.db.prepare(
      'UPDATE collections SET name = ?, description = ?, prompt_ids = ?, is_public = ?, author_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?'
    );

    stmt.run(
      updates.name ?? current.name,
      updates.description ?? current.description,
      JSON.stringify(updates.promptIds ?? current.promptIds),
      updates.isPublic ?? current.isPublic,
      updates.authorId ?? current.authorId,
      id
    );

    return this.getCollection(id);
  }

  deleteCollection(id: string): void {
    const stmt = this.db.prepare('DELETE FROM collections WHERE id = ?');
    stmt.run(id);
  }

  listCollections(): Collection[] {
    const stmt = this.db.prepare('SELECT * FROM collections ORDER BY updated_at DESC');
    const rows = stmt.all() as any[];

    return rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      promptIds: JSON.parse(row.prompt_ids || '[]'),
      isPublic: row.is_public,
      authorId: row.author_id,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }));
  }

  // Utility methods
  incrementUsageCount(promptId: string): void {
    const stmt = this.db.prepare('UPDATE prompts SET usage_count = usage_count + 1 WHERE id = ?');
    stmt.run(promptId);
  }

  updateRating(promptId: string, rating: number): void {
    if (rating < 0 || rating > 5) {
      throw new Error('Rating must be between 0 and 5');
    }
    const stmt = this.db.prepare('UPDATE prompts SET rating = ? WHERE id = ?');
    stmt.run(rating, promptId);
  }

  getPromptsByCategory(categoryId: string): Prompt[] {
    return this.listPrompts({ category: categoryId });
  }

  getPromptsByAuthor(author: string): Prompt[] {
    return this.listPrompts({ author });
  }

  getRecentPrompts(limit: number = 10): Prompt[] {
    const stmt = this.db.prepare('SELECT * FROM prompts ORDER BY updated_at DESC LIMIT ?');
    const rows = stmt.all(limit) as any[];

    return rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      content: row.content,
      category: row.category_id,
      tags: JSON.parse(row.tags || '[]'),
      author: row.author,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      version: row.version,
      isPrivate: row.is_private,
      usageCount: row.usage_count,
      rating: row.rating,
      metadata: JSON.parse(row.metadata || '{}')
    }));
  }

  getPopularPrompts(limit: number = 10): Prompt[] {
    const stmt = this.db.prepare('SELECT * FROM prompts ORDER BY usage_count DESC, rating DESC LIMIT ?');
    const rows = stmt.all(limit) as any[];

    return rows.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      content: row.content,
      category: row.category_id,
      tags: JSON.parse(row.tags || '[]'),
      author: row.author,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      version: row.version,
      isPrivate: row.is_private,
      usageCount: row.usage_count,
      rating: row.rating,
      metadata: JSON.parse(row.metadata || '{}')
    }));
  }

  // Initialize default categories
  initializeDefaultCategories(): void {
    const defaultCategories = [
      { name: 'General', description: 'General purpose prompts', sortOrder: 1, isSystem: true },
      { name: 'Development', description: 'Software development prompts', sortOrder: 2, isSystem: true },
      { name: 'Writing', description: 'Content writing and editing prompts', sortOrder: 3, isSystem: true },
      { name: 'Analysis', description: 'Data analysis and research prompts', sortOrder: 4, isSystem: true },
      { name: 'Creative', description: 'Creative and artistic prompts', sortOrder: 5, isSystem: true }
    ];

    for (const category of defaultCategories) {
      try {
        // Check if category already exists
        const existing = this.db.prepare('SELECT id FROM categories WHERE name = ?').get(category.name);
        if (!existing) {
          this.createCategory({
            name: category.name,
            description: category.description,
            sortOrder: category.sortOrder,
            isSystem: category.isSystem,
            parentId: undefined,
            icon: undefined,
            color: undefined
          });
        }
      } catch (error) {
        console.warn(`Failed to create default category ${category.name}:`, error);
      }
    }
  }

  close(): void {
    this.db.close();
  }
}