import Database from 'better-sqlite3';
import { Prompt, Category, Collection, SearchFilters } from '../types';
import { v4 as uuidv4 } from 'uuid';

export class PromptDatabase {
  private db: Database.Database;

  constructor(dbPath: string = './data/library.db') {
    this.db = new Database(dbPath);
    this.initializeTables();
  }

  private initializeTables(): void {
    // 创建分类表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        parent_id TEXT,
        icon TEXT,
        color TEXT,
        sort_order INTEGER DEFAULT 0,
        is_system BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES categories(id)
      )
    `);

    // 创建提示词表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS prompts (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        content TEXT NOT NULL,
        category_id TEXT,
        tags TEXT, -- JSON array
        author TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        version INTEGER DEFAULT 1,
        is_private BOOLEAN DEFAULT FALSE,
        usage_count INTEGER DEFAULT 0,
        rating REAL DEFAULT 0,
        metadata TEXT, -- JSON object
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )
    `);

    // 创建集合表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS collections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        prompt_ids TEXT, -- JSON array
        is_public BOOLEAN DEFAULT TRUE,
        author_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        tags TEXT -- JSON array
      )
    `);

    // 创建全文搜索索引
    this.db.exec(`
      CREATE VIRTUAL TABLE IF NOT EXISTS prompts_fts USING fts5(
        title, description, content, tags, author,
        content='prompts',
        content_rowid='rowid'
      )
    `);

    // 创建触发器保持FTS同步
    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS prompts_ai AFTER INSERT ON prompts BEGIN
        INSERT INTO prompts_fts(rowid, title, description, content, tags, author)
        VALUES (new.rowid, new.title, new.description, new.content, new.tags, new.author);
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS prompts_ad AFTER DELETE ON prompts BEGIN
        INSERT INTO prompts_fts(prompts_fts, rowid, title, description, content, tags, author)
        VALUES ('delete', old.rowid, old.title, old.description, old.content, old.tags, old.author);
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS prompts_au AFTER UPDATE ON prompts BEGIN
        INSERT INTO prompts_fts(prompts_fts, rowid, title, description, content, tags, author)
        VALUES ('delete', old.rowid, old.title, old.description, old.content, old.tags, old.author);
        INSERT INTO prompts_fts(rowid, title, description, content, tags, author)
        VALUES (new.rowid, new.title, new.description, new.content, new.tags, new.author);
      END;
    `);

    // 插入默认分类
    this.insertDefaultCategories();
  }

  private insertDefaultCategories(): void {
    const defaultCategories = [
      { id: 'general', name: 'General', description: 'General purpose prompts', isSystem: true },
      { id: 'coding', name: 'Coding', description: 'Programming and development prompts', isSystem: true },
      { id: 'writing', name: 'Writing', description: 'Content creation and writing prompts', isSystem: true },
      { id: 'analysis', name: 'Analysis', description: 'Data analysis and research prompts', isSystem: true },
      { id: 'creative', name: 'Creative', description: 'Creative and artistic prompts', isSystem: true }
    ];

    const insertCategory = this.db.prepare(`
      INSERT OR IGNORE INTO categories (id, name, description, is_system, sort_order)
      VALUES (?, ?, ?, ?, ?)
    `);

    defaultCategories.forEach((cat, index) => {
      insertCategory.run(cat.id, cat.name, cat.description, cat.isSystem, index);
    });
  }

  // Prompt CRUD operations
  createPrompt(prompt: Omit<Prompt, 'id' | 'createdAt' | 'updatedAt'>): Prompt {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO prompts (id, title, description, content, category_id, tags, author, 
                          created_at, updated_at, version, is_private, usage_count, rating, metadata)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id, prompt.title, prompt.description, prompt.content, prompt.categoryId,
      JSON.stringify(prompt.tags), prompt.author, now, now, prompt.version,
      prompt.isPrivate, prompt.usageCount, prompt.rating, JSON.stringify(prompt.metadata)
    );

    return this.getPrompt(id)!;
  }

  getPrompt(id: string): Prompt | null {
    const stmt = this.db.prepare('SELECT * FROM prompts WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) return null;
    
    return this.mapRowToPrompt(row);
  }

  listPrompts(filters?: SearchFilters): Prompt[] {
    let query = 'SELECT * FROM prompts WHERE 1=1';
    const params: any[] = [];

    if (filters?.category) {
      query += ' AND category_id = ?';
      params.push(filters.category);
    }

    if (filters?.author) {
      query += ' AND author = ?';
      params.push(filters.author);
    }

    query += ' ORDER BY updated_at DESC';

    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as any[];
    
    return rows.map(row => this.mapRowToPrompt(row));
  }

  searchPrompts(query: string): Prompt[] {
    const stmt = this.db.prepare(`
      SELECT p.* FROM prompts p
      JOIN prompts_fts fts ON p.rowid = fts.rowid
      WHERE prompts_fts MATCH ?
      ORDER BY rank
    `);
    
    const rows = stmt.all(query) as any[];
    return rows.map(row => this.mapRowToPrompt(row));
  }

  updatePrompt(id: string, updates: Partial<Prompt>): Prompt | null {
    const current = this.getPrompt(id);
    if (!current) return null;

    const now = new Date().toISOString();
    const stmt = this.db.prepare(`
      UPDATE prompts 
      SET title = ?, description = ?, content = ?, category_id = ?, tags = ?, 
          updated_at = ?, version = ?, is_private = ?, metadata = ?
      WHERE id = ?
    `);

    stmt.run(
      updates.title ?? current.title,
      updates.description ?? current.description,
      updates.content ?? current.content,
      updates.categoryId ?? current.categoryId,
      JSON.stringify(updates.tags ?? current.tags),
      now,
      (updates.version ?? current.version) + 1,
      updates.isPrivate ?? current.isPrivate,
      JSON.stringify(updates.metadata ?? current.metadata),
      id
    );

    return this.getPrompt(id);
  }

  deletePrompt(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM prompts WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Category operations
  createCategory(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Category {
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO categories (id, name, description, parent_id, icon, color, sort_order, is_system, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id, category.name, category.description, category.parentId,
      category.icon, category.color, category.sortOrder, category.isSystem, now, now
    );

    return this.getCategory(id)!;
  }

  getCategory(id: string): Category | null {
    const stmt = this.db.prepare('SELECT * FROM categories WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) return null;
    
    return this.mapRowToCategory(row);
  }

  listCategories(): Category[] {
    const stmt = this.db.prepare('SELECT * FROM categories ORDER BY sort_order, name');
    const rows = stmt.all() as any[];
    
    return rows.map(row => this.mapRowToCategory(row));
  }

  private mapRowToPrompt(row: any): Prompt {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      content: row.content,
      categoryId: row.category_id,
      tags: JSON.parse(row.tags || '[]'),
      author: row.author,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      version: row.version,
      isPrivate: Boolean(row.is_private),
      usageCount: row.usage_count,
      rating: row.rating,
      metadata: JSON.parse(row.metadata || '{}')
    };
  }

  private mapRowToCategory(row: any): Category {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      parentId: row.parent_id,
      icon: row.icon,
      color: row.color,
      sortOrder: row.sort_order,
      isSystem: Boolean(row.is_system),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  close(): void {
    this.db.close();
  }
}
