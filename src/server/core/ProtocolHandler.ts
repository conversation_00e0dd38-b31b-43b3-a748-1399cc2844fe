import { ServerCapabilities } from '../../types';
import { PromptDatabase } from '../../database/Database';

export class ProtocolHandler {
  private capabilities: ServerCapabilities;
  private database: PromptDatabase;

  constructor() {
    this.database = new PromptDatabase();
    this.capabilities = {
      tools: {
        "list-prompts": {
          inputSchema: {
            type: "object",
            properties: {
              category: { type: "string" },
              author: { type: "string" },
              limit: { type: "number" },
              offset: { type: "number" }
            }
          },
          description: "List all available prompts with optional filtering",
        },
        "search-prompts": {
          inputSchema: {
            type: "object",
            properties: {
              query: { type: "string" }
            },
            required: ["query"]
          },
          description: "Search prompts using full-text search",
        },
        "get-prompt": {
          inputSchema: {
            type: "object",
            properties: {
              id: { type: "string" }
            },
            required: ["id"]
          },
          description: "Get a specific prompt by ID",
        },
        "create-prompt": {
          inputSchema: {
            type: "object",
            properties: {
              title: { type: "string" },
              content: { type: "string" },
              description: { type: "string" },
              categoryId: { type: "string" },
              tags: { type: "array", items: { type: "string" } },
              author: { type: "string" },
              isPrivate: { type: "boolean" },
              metadata: { type: "object" }
            },
            required: ["title", "content", "author"]
          },
          description: "Create a new prompt",
        },
        "update-prompt": {
          inputSchema: {
            type: "object",
            properties: {
              id: { type: "string" },
              title: { type: "string" },
              content: { type: "string" },
              description: { type: "string" },
              categoryId: { type: "string" },
              tags: { type: "array", items: { type: "string" } },
              isPrivate: { type: "boolean" },
              metadata: { type: "object" }
            },
            required: ["id"]
          },
          description: "Update an existing prompt",
        },
        "delete-prompt": {
          inputSchema: {
            type: "object",
            properties: {
              id: { type: "string" }
            },
            required: ["id"]
          },
          description: "Delete a prompt",
        }
      },
      resources: {
        prompts: {
          templates: ["prompts://{id}", "prompts://category/{category}"],
          description: "Access individual prompts and categories",
        },
      },
      prompts: {
        "list-prompts": {
          description: "List all available prompts",
          arguments: {},
        },
        "search-prompts": {
          description: "Search prompts by query",
          arguments: {},
        },
      },
    };
  }

  getCapabilities(): ServerCapabilities {
    return this.capabilities;
  }

  handleRequest(request: any): any {
    try {
      const { method, params } = request;
      
      switch (method) {
        case 'mcp.getCapabilities':
          return { capabilities: this.getCapabilities() };
          
        case 'mcp.listPrompts':
          const prompts = this.database.listPrompts(params);
          return { success: true, data: prompts };
          
        case 'mcp.searchPrompts':
          const searchResults = this.database.searchPrompts(params.query);
          return { success: true, data: searchResults };
          
        case 'mcp.getPrompt':
          const prompt = this.database.getPrompt(params.id);
          if (!prompt) {
            throw new Error('Prompt not found');
          }
          return { success: true, data: prompt };
          
        case 'mcp.createPrompt':
          const newPrompt = this.database.createPrompt({
            title: params.title,
            content: params.content,
            description: params.description || '',
            categoryId: params.categoryId || 'general',
            tags: params.tags || [],
            author: params.author,
            version: 1,
            isPrivate: params.isPrivate || false,
            usageCount: 0,
            rating: 0,
            metadata: params.metadata || {}
          });
          return { success: true, data: newPrompt };
          
        case 'mcp.updatePrompt':
          const updatedPrompt = this.database.updatePrompt(params.id, params);
          if (!updatedPrompt) {
            throw new Error('Prompt not found');
          }
          return { success: true, data: updatedPrompt };
          
        case 'mcp.deletePrompt':
          const deleted = this.database.deletePrompt(params.id);
          if (!deleted) {
            throw new Error('Prompt not found');
          }
          return { success: true, message: 'Prompt deleted successfully' };
          
        default:
          throw new Error(`Unknown method: ${method}`);
      }
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  close(): void {
    this.database.close();
  }
}
