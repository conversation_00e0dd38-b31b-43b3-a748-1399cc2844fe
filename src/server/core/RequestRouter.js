"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestRouter = void 0;
const Database_1 = require("../../database/Database");
class RequestRouter {
    constructor(protocolHandler) {
        this.protocolHandler = protocolHandler;
    }
    routeRequest(request) {
        const { method, params } = request;
        switch (method) {
            case 'list-prompts':
                return this.handleListPrompts(params);
            case 'search-prompts':
                return this.handleSearchPrompts(params);
            case 'mcp.getCapabilities':
                return this.protocolHandler.handleRequest(request);
            default:
                throw new Error(`Unsupported method: ${method}`);
        }
    }
    handleListPrompts(params) {
        const database = new Database_1.PromptDatabase();
        try {
            // Validate input parameters
            const validatedParams = {
                limit: params.limit ? Math.min(Number(params.limit), 100) : 20,
                offset: params.offset ? Math.max(0, Number(params.offset)) : 0,
                category: params.category || undefined,
                author: params.author || undefined,
                isPrivate: params.isPrivate !== undefined ? Boolean(params.isPrivate) : undefined,
                sortBy: ['createdAt', 'updatedAt', 'usageCount'].includes(params.sortBy)
                    ? params.sortBy
                    : 'createdAt',
                sortOrder: params.sortOrder === 'asc' ? 'asc' : 'desc'
            };
            // Only pass supported filter parameters to database
            const filter = {};
            if (validatedParams.category)
                filter.category = validatedParams.category;
            if (validatedParams.author)
                filter.author = validatedParams.author;
            if (validatedParams.isPrivate !== undefined)
                filter.isPrivate = validatedParams.isPrivate;
            const prompts = database.listPrompts(filter);
            return {
                success: true,
                data: prompts,
                pagination: {
                    limit: validatedParams.limit,
                    offset: validatedParams.offset
                }
            };
        }
        finally {
            database.close();
        }
    }
    handleSearchPrompts(params) {
        if (!params.query || typeof params.query !== 'string' || params.query.trim().length < 2) {
            throw new Error('Search query must be a string with at least 2 characters');
        }
        const database = new Database_1.PromptDatabase();
        try {
            const validatedParams = {
                query: params.query.trim(),
                limit: params.limit ? Math.min(Number(params.limit), 50) : 10,
                threshold: params.threshold ? Math.min(Math.max(Number(params.threshold), 0.1), 1) : 0.5
            };
            const prompts = database.searchPrompts(validatedParams.query);
            return {
                success: true,
                data: prompts,
                meta: {
                    query: validatedParams.query,
                    limit: validatedParams.limit
                }
            };
        }
        finally {
            database.close();
        }
    }
}
exports.RequestRouter = RequestRouter;
