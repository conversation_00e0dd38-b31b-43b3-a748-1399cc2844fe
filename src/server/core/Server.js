"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Server = void 0;
const express_1 = __importDefault(require("express"));
const ProtocolHandler_1 = require("./ProtocolHandler");
const RequestRouter_1 = require("./RequestRouter");
const AuthMiddleware_1 = require("../middleware/AuthMiddleware");
class Server {
    constructor() {
        this.app = (0, express_1.default)();
        this.protocolHandler = new ProtocolHandler_1.ProtocolHandler();
        this.requestRouter = new RequestRouter_1.RequestRouter(this.protocolHandler);
        this.app.use(express_1.default.json());
        this.app.post('/mcp', AuthMiddleware_1.authenticate, (req, res) => {
            try {
                const response = this.handleRequest(req.body);
                res.json(response);
            }
            catch (error) {
                res.status(400).json({ error: error.message });
            }
        });
        this.app.get('/prompts', (req, res) => {
            try {
                const database = new (require('../../database/Database').PromptDatabase)();
                const prompts = database.listPrompts({});
                res.json({ success: true, data: prompts });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        this.app.get('/collections', (req, res) => {
            try {
                const database = new (require('../../database/Database').PromptDatabase)();
                const collections = database.listCollections();
                res.json({ success: true, data: collections });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        this.app.get('/categories', (req, res) => {
            try {
                const database = new (require('../../database/Database').PromptDatabase)();
                const categories = database.listCategories();
                res.json({ success: true, data: categories });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        this.app.get('/', (req, res) => {
            var _a, _b, _c;
            try {
                const routes = ((_c = (_b = (_a = this.app._router) === null || _a === void 0 ? void 0 : _a.stack) === null || _b === void 0 ? void 0 : _b.filter((layer) => layer.route)) === null || _c === void 0 ? void 0 : _c.map((layer) => {
                    const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
                    return {
                        path: layer.route.path,
                        method: methods,
                        description: `Endpoint for ${layer.route.path}`
                    };
                })) || [];
                // Ensure all expected routes are included
                const expectedRoutes = ['/mcp', '/prompts', '/collections', '/categories'];
                const missingRoutes = expectedRoutes.filter(route => !routes.some((r) => r.path === route));
                if (missingRoutes.length > 0) {
                    console.warn('Warning: The following routes are not registered in the router:', missingRoutes);
                }
                res.json({
                    message: 'Prompt Library MCP Server',
                    version: '1.0.0',
                    endpoints: routes,
                    missingRoutes: missingRoutes
                });
            }
            catch (error) {
                console.error('Error in root route:', error.message);
                res.status(500).json({
                    error: 'Internal server error',
                    message: 'Prompt Library MCP Server',
                    version: '1.0.0'
                });
            }
        });
    }
    start(port = 3000) {
        return new Promise((resolve) => {
            this.server = this.app.listen(port, () => {
                var _a, _b, _c;
                console.log(`Server started on port ${port}`);
                console.log('Debug: Attempting to connect to database...');
                try {
                    const database = new (require('../../database/Database').PromptDatabase)();
                    console.log('Debug: Database connection successful');
                }
                catch (error) {
                    console.error('Debug: Database connection failed:', error.message);
                }
                // Log registered routes for debugging
                try {
                    const routes = ((_c = (_b = (_a = this.app._router) === null || _a === void 0 ? void 0 : _a.stack) === null || _b === void 0 ? void 0 : _b.filter((layer) => layer.route)) === null || _c === void 0 ? void 0 : _c.map((layer) => {
                        const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
                        return {
                            path: layer.route.path,
                            method: methods,
                            description: `Endpoint for ${layer.route.path}`
                        };
                    })) || [];
                    console.log('Debug: Registered routes:', routes);
                }
                catch (error) {
                    console.error('Debug: Failed to log routes:', error.message);
                }
                resolve();
            });
        });
    }
    stop() {
        if (this.server) {
            this.server.close();
        }
    }
    getExpressApp() {
        return this.app;
    }
    handleRequest(request) {
        return this.requestRouter.routeRequest(request);
    }
}
exports.Server = Server;
