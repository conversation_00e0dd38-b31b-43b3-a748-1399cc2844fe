"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Server = void 0;
const express_1 = __importDefault(require("express"));
const ProtocolHandler_1 = require("./ProtocolHandler");
const RequestRouter_1 = require("./RequestRouter");
const AuthMiddleware_1 = require("../middleware/AuthMiddleware");
class Server {
    constructor() {
        this.app = (0, express_1.default)();
        this.protocolHandler = new ProtocolHandler_1.ProtocolHandler();
        this.requestRouter = new RequestRouter_1.RequestRouter(this.protocolHandler);
        this.app.use(express_1.default.json());
        this.app.post('/mcp', AuthMiddleware_1.authenticate, (req, res) => {
            try {
                const response = this.handleRequest(req.body);
                res.json(response);
            }
            catch (error) {
                res.status(400).json({ error: error.message });
            }
        });
        this.app.get('/prompts', (req, res) => {
            try {
                const database = new (require('../../database/Database').PromptDatabase)();
                const prompts = database.listPrompts({});
                res.json({ success: true, data: prompts });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        this.app.get('/collections', (req, res) => {
            try {
                const database = new (require('../../database/Database').PromptDatabase)();
                const collections = database.listCollections({});
                res.json({ success: true, data: collections });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        this.app.get('/categories', (req, res) => {
            try {
                const database = new (require('../../database/Database').PromptDatabase)();
                const categories = database.listCategories({});
                res.json({ success: true, data: categories });
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        this.app.get('/', (req, res) => {
            const routes = this.app._router.stack
                .filter((layer) => layer.route)
                .map((layer) => {
                const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
                return {
                    path: layer.route.path,
                    method: methods,
                    description: `Endpoint for ${layer.route.path}`
                };
            });
            res.json({
                message: 'Prompt Library MCP Server',
                version: '1.0.0',
                endpoints: routes
            });
        });
    }
    start(port = 3000) {
        return new Promise((resolve) => {
            this.server = this.app.listen(port, () => {
                console.log(`Server started on port ${port}`);
                console.log('Debug: Attempting to connect to database...');
                try {
                    const database = new (require('../../database/Database').PromptDatabase)();
                    console.log('Debug: Database connection successful');
                }
                catch (error) {
                    console.error('Debug: Database connection failed:', error.message);
                }

                // Log registered routes for debugging (after server initialization)
                process.nextTick(() => {
                    try {
                        const routes = this.app._router.stack
                            .filter((layer) => layer.route)
                            .map((layer) => {
                                const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
                                return {
                                    path: layer.route.path,
                                    method: methods,
                                    description: `Endpoint for ${layer.route.path}`
                                };
                            });
                        console.log('Debug: Registered routes:', routes);
                    } catch (error) {
                        console.error('Debug: Failed to log routes:', error.message);
                    }
                });

                resolve();
            });
        });
    }
    stop() {
        if (this.server) {
            this.server.close();
        }
    }
    getExpressApp() {
        return this.app;
    }
    handleRequest(request) {
        return this.requestRouter.routeRequest(request);
    }
}
exports.Server = Server;
