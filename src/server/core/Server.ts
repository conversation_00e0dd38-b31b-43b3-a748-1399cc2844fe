import express from 'express';
import { <PERSON><PERSON><PERSON><PERSON> } from './ProtocolHandler';
import { RequestRouter } from './RequestRouter';
import { authenticate } from '../middleware/AuthMiddleware';

export class Server {
  private app: express.Application;
  private protocolHandler: ProtocolHandler;
  private requestRouter: RequestRouter;
  private server: any;

  constructor() {
    this.app = express();
    this.protocolHandler = new ProtocolHandler();
    this.requestRouter = new RequestRouter(this.protocolHandler);

    this.app.use(express.json());
    this.app.post('/mcp', authenticate, (req, res) => {
      try {
        const response = this.handleRequest(req.body);
        res.json(response);
      } catch (error: any) {
        res.status(400).json({ error: error.message });
      }
    });
    
    this.app.get('/prompts', (req, res) => {
      try {
        const database = new (require('../../database/Database').PromptDatabase)();
        const prompts = database.listPrompts({});
        res.json({ success: true, data: prompts });
      } catch (error: any) {
        res.status(500).json({ error: error.message });
      }
    });
    
    this.app.get('/collections', (req, res) => {
      try {
        const database = new (require('../../database/Database').PromptDatabase)();
        const collections = database.listCollections();
        res.json({ success: true, data: collections });
      } catch (error: any) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/categories', (req, res) => {
      try {
        const database = new (require('../../database/Database').PromptDatabase)();
        const categories = database.listCategories();
        res.json({ success: true, data: categories });
      } catch (error: any) {
        res.status(500).json({ error: error.message });
      }
    });
    
    this.app.get('/', (req, res) => {
      try {
        const routes = this.app._router?.stack
          ?.filter((layer: any) => layer.route)
          ?.map((layer: any) => {
            const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
            return {
              path: layer.route.path,
              method: methods,
              description: `Endpoint for ${layer.route.path}`
            };
          }) || [];

        // Ensure all expected routes are included
        const expectedRoutes = ['/mcp', '/prompts', '/collections', '/categories'];
        const missingRoutes = expectedRoutes.filter(route => !routes.some((r: any) => r.path === route));

        if (missingRoutes.length > 0) {
          console.warn('Warning: The following routes are not registered in the router:', missingRoutes);
        }

        res.json({
          message: 'Prompt Library MCP Server',
          version: '1.0.0',
          endpoints: routes,
          missingRoutes: missingRoutes
        });
      } catch (error: any) {
        console.error('Error in root route:', error.message);
        res.status(500).json({
          error: 'Internal server error',
          message: 'Prompt Library MCP Server',
          version: '1.0.0'
        });
      }
    });
  }

  start(port: number = 3000): Promise<void> {
    return new Promise((resolve) => {
      this.server = this.app.listen(port, () => {
        console.log(`Server started on port ${port}`);
        console.log('Debug: Attempting to connect to database...');
        try {
          const database = new (require('../../database/Database').PromptDatabase)();
          console.log('Debug: Database connection successful');
        } catch (error: any) {
          console.error('Debug: Database connection failed:', error.message);
        }

        // Log registered routes for debugging
        try {
          const routes = this.app._router?.stack
            ?.filter((layer: any) => layer.route)
            ?.map((layer: any) => {
              const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
              return {
                path: layer.route.path,
                method: methods,
                description: `Endpoint for ${layer.route.path}`
              };
            }) || [];
          console.log('Debug: Registered routes:', routes);
        } catch (error: any) {
          console.error('Debug: Failed to log routes:', error.message);
        }

        resolve();
      });
    });
  }

  stop(): void {
    if (this.server) {
      this.server.close();
    }
  }

  getExpressApp(): express.Application {
    return this.app;
  }

  handleRequest(request: any): any {
    return this.requestRouter.routeRequest(request);
  }
}