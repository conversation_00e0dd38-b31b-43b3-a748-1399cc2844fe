export interface Prompt {
  id: string;
  title: string;
  description: string;
  content: string;
  categoryId: string;
  tags: string[];
  author: string;
  createdAt: Date;
  updatedAt: Date;
  version: number;
  isPrivate: boolean;
  usageCount: number;
  rating: number;
  metadata: PromptMetadata;
}

export interface PromptMetadata {
  language?: string;
  framework?: string;
  complexity?: 'beginner' | 'intermediate' | 'advanced';
  estimatedTokens?: number;
  variables?: string[];
  outputFormat?: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Collection {
  id: string;
  name: string;
  description: string;
  promptIds: string[];
  isPublic: boolean;
  authorId: string;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
}

export interface SearchFilters {
  category?: string;
  tags?: string[];
  author?: string;
  complexity?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  rating?: {
    min: number;
    max: number;
  };
}

export interface ServerCapabilities {
  tools: Record<string, any>;
  resources: Record<string, any>;
  prompts: Record<string, any>;
}