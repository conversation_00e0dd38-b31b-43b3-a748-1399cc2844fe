["AbortController", "AbortSignal", "AbsoluteOrientationSensor", "AbstractRange", "Accelerometer", "AI", "AICreateMonitor", "AITextSession", "AnalyserNode", "Animation", "AnimationEffect", "AnimationEvent", "AnimationPlaybackEvent", "AnimationTimeline", "AsyncDisposableStack", "Attr", "Audio", "AudioBuffer", "AudioBufferSourceNode", "AudioContext", "AudioData", "AudioDecoder", "AudioDestinationNode", "AudioEncoder", "AudioListener", "AudioNode", "AudioParam", "AudioParamMap", "AudioProcessingEvent", "AudioScheduledSourceNode", "AudioSinkInfo", "AudioWorklet", "AudioWorkletGlobalScope", "AudioWorkletNode", "AudioWorkletProcessor", "AuthenticatorAssertionResponse", "AuthenticatorAttestationResponse", "AuthenticatorResponse", "BackgroundFetchManager", "BackgroundFetchRecord", "BackgroundFetchRegistration", "BarcodeDetector", "BarProp", "BaseAudioContext", "BatteryManager", "BeforeUnloadEvent", "BiquadFilterNode", "Blob", "BlobEvent", "Bluetooth", "BluetoothCharacteristicProperties", "BluetoothDevice", "BluetoothRemoteGATTCharacteristic", "BluetoothRemoteGATTDescriptor", "BluetoothRemoteGATTServer", "BluetoothRemoteGATTService", "BluetoothUUID", "BroadcastChannel", "BrowserCaptureMediaStreamTrack", "ByteLengthQueuingStrategy", "<PERSON><PERSON>", "CacheStorage", "CanvasCaptureMediaStream", "CanvasCaptureMediaStreamTrack", "CanvasGradient", "CanvasPattern", "CanvasRenderingContext2D", "CaptureController", "CaretPosition", "CDATASection", "ChannelMergerNode", "ChannelSplitterNode", "ChapterInformation", "CharacterBoundsUpdateEvent", "CharacterData", "Clipboard", "ClipboardEvent", "ClipboardItem", "CloseEvent", "CloseWatcher", "CommandEvent", "Comment", "CompositionEvent", "CompressionStream", "ConstantSourceNode", "ContentVisibilityAutoStateChangeEvent", "ConvolverNode", "<PERSON>ie<PERSON>hange<PERSON>vent", "CookieDeprecationLabel", "CookieStore", "<PERSON><PERSON>", "CountQueuingStrategy", "Credential", "CredentialsContainer", "CropTarget", "Crypto", "CryptoKey", "CSPViolationReportBody", "CSS", "CSSAnimation", "CSSConditionRule", "CSSContainerRule", "CSSCounterStyleRule", "CSSFontFaceRule", "CSSFontFeatureValuesRule", "CSSFontPaletteValuesRule", "CSSGroupingRule", "CSSImageValue", "CSSImportRule", "CSSKeyframeRule", "CSSKeyframesRule", "CSSKeywordValue", "CSSLayerBlockRule", "CSSLayerStatementRule", "CSSMarginRule", "CSSMathClamp", "CSSMathInvert", "CSSMathMax", "CSSMathMin", "CSSMathNegate", "CSSMathProduct", "CSSMathSum", "CSSMathValue", "CSSMatrixComponent", "CSSMediaRule", "CSSNamespaceRule", "CSSNestedDeclarations", "CSSNumericArray", "CSSNumericValue", "CSSPageDescriptors", "CSSPageRule", "CSSPerspective", "CSSPositionTryDescriptors", "CSSPositionTryRule", "CSSPositionValue", "CSSPropertyRule", "CSSRotate", "CSSRule", "CSSRuleList", "CSSScale", "CSSScopeRule", "CSSSkew", "CSSSkewX", "CSSSkewY", "CSSStartingStyleRule", "CSSStyleDeclaration", "CSSStyleRule", "CSSStyleSheet", "CSSStyleValue", "CSSSupportsRule", "CSSTransformComponent", "CSSTransformValue", "CSSTransition", "CSSTranslate", "CSSUnitValue", "CSSUnparsedValue", "CSSVariableReferenceValue", "CSSViewTransitionRule", "CustomElementRegistry", "CustomEvent", "CustomStateSet", "DataTransfer", "DataTransferItem", "DataTransferItemList", "DecompressionStream", "DelayNode", "DelegatedInkTrailPresenter", "DeviceMotionEvent", "DeviceMotionEventAcceleration", "DeviceMotionEventRotationRate", "DeviceOrientationEvent", "DevicePosture", "DisposableStack", "Document", "DocumentFragment", "DocumentPictureInPicture", "DocumentPictureInPictureEvent", "DocumentTimeline", "DocumentType", "DOMError", "DOMException", "DOMImplementation", "DOMMatrix", "DOMMatrixReadOnly", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DOMPoint", "DOMPointReadOnly", "DOMQuad", "DOMRect", "DOMRectList", "DOMRectReadOnly", "DOMStringList", "DOMStringMap", "DOMTokenList", "DragEvent", "DynamicsCompressorNode", "EditContext", "Element", "ElementInternals", "EncodedAudioChunk", "EncodedVideoChunk", "ErrorEvent", "Event", "EventCounts", "EventSource", "EventTarget", "External", "EyeDropper", "FeaturePolicy", "FederatedCredential", "<PERSON><PERSON>", "FencedFrameConfig", "FetchLaterResult", "File", "FileList", "FileReader", "FileSystem", "FileSystemDirectoryEntry", "FileSystemDirectoryHandle", "FileSystemDirectoryReader", "FileSystemEntry", "FileSystemFileEntry", "FileSystemFileHandle", "FileSystemHandle", "FileSystemObserver", "FileSystemWritableFileStream", "FocusEvent", "FontData", "FontFace", "FontFaceSet", "FontFaceSetLoadEvent", "FormData", "FormDataEvent", "FragmentDirective", "GainNode", "Gamepad", "GamepadAxisMoveEvent", "GamepadButton", "GamepadButtonEvent", "GamepadEvent", "GamepadHapticActuator", "GamepadPose", "Geolocation", "GeolocationCoordinates", "GeolocationPosition", "GeolocationPositionError", "GPU", "GPUAdapter", "GPUAdapterInfo", "GPUBindGroup", "GPUBindGroupLayout", "GPUBuffer", "GPUBufferUsage", "GPUCanvasContext", "GPUColorWrite", "GPUCommand<PERSON>uffer", "GPUCommandEncoder", "GPUCompilationInfo", "GPUCompilationMessage", "GPUComputePassEncoder", "GPUComputePipeline", "GPUDevice", "GPUDeviceLostInfo", "GPUError", "GPUExternalTexture", "GPUInternalError", "GPUMapMode", "GPUOutOfMemoryError", "GPUPipelineError", "GPUPipelineLayout", "GPUQuerySet", "GPUQueue", "GPURenderBundle", "GPURenderBundleEncoder", "GPURenderPassEncoder", "GPURenderPipeline", "GPUSampler", "GPUShaderModule", "GPUShaderStage", "GPUSupportedFeatures", "GPUSupportedLimits", "GPUTexture", "GPUTextureUsage", "GPUTextureView", "GPUUncapturedErrorEvent", "GPUValidationError", "GravitySensor", "Gyroscope", "HashChangeEvent", "Headers", "HID", "HIDConnectionEvent", "HIDDevice", "HIDInputReportEvent", "Highlight", "HighlightRegistry", "History", "HTMLAllCollection", "HTMLAnchorElement", "HTMLAreaElement", "HTMLAudioElement", "HTMLBaseElement", "HTMLBodyElement", "HTMLBRElement", "HTMLButtonElement", "HTMLCanvasElement", "HTMLCollection", "HTMLDataElement", "HTMLDataListElement", "HTMLDetailsElement", "HTMLDialogElement", "HTMLDirectoryElement", "HTMLDivElement", "HTMLDListElement", "HTMLDocument", "HTMLElement", "HTMLEmbedElement", "HTMLFencedFrameElement", "HTMLFieldSetElement", "HTMLFontElement", "HTMLFormControlsCollection", "HTMLFormElement", "HTMLFrameElement", "HTMLFrameSetElement", "HTMLHeadElement", "HTMLHeadingElement", "HTMLHRElement", "HTMLHtmlElement", "HTMLIFrameElement", "HTMLImageElement", "HTMLInputElement", "HTMLLabelElement", "HTMLLegendElement", "HTMLLIElement", "HTMLLinkElement", "HTMLMapElement", "HTMLMarqueeElement", "HTMLMediaElement", "HTMLMenuElement", "HTMLMetaElement", "HTMLMeterElement", "HTMLModElement", "HTMLObjectElement", "HTMLOListElement", "HTMLOptGroupElement", "HTMLOptionElement", "HTMLOptionsCollection", "HTMLOutputElement", "HTMLParagraphElement", "HTMLParamElement", "HTMLPictureElement", "HTMLPreElement", "HTMLProgressElement", "HTMLQuoteElement", "HTMLScriptElement", "HTMLSelectedContentElement", "HTMLSelectElement", "HTMLSlotElement", "HTMLSourceElement", "HTMLSpanElement", "HTMLStyleElement", "HTMLTableCaptionElement", "HTMLTableCellElement", "HTMLTableColElement", "HTMLTableElement", "HTMLTableRowElement", "HTMLTableSectionElement", "HTMLTemplateElement", "HTMLTextAreaElement", "HTMLTimeElement", "HTMLTitleElement", "HTMLTrackElement", "HTMLUListElement", "HTMLUnknownElement", "HTMLVideoElement", "IDBCursor", "IDBCursorWithValue", "IDBDatabase", "IDBFactory", "IDBIndex", "IDBKeyRange", "IDBObjectStore", "IDBOpenDBRequest", "IDBRequest", "IDBTransaction", "IDBVersionChangeEvent", "IdentityCredential", "IdentityCredentialError", "IdentityProvider", "IdleDeadline", "IdleDetector", "IIRFilterNode", "Image", "ImageBitmap", "ImageBitmapRenderingContext", "ImageCapture", "ImageData", "ImageDecoder", "ImageTrack", "ImageTrackList", "Ink", "InputDeviceCapabilities", "InputDeviceInfo", "InputEvent", "IntersectionObserver", "IntersectionObserverEntry", "Keyboard", "KeyboardEvent", "KeyboardLayoutMap", "KeyframeEffect", "LanguageDetector", "LargestContentfulPaint", "LaunchParams", "LaunchQueue", "LayoutShift", "LayoutShiftAttribution", "LinearAccelerationSensor", "Location", "Lock", "LockManager", "MathMLElement", "MediaCapabilities", "MediaCapabilitiesInfo", "MediaDeviceInfo", "MediaDevices", "MediaElementAudioSourceNode", "MediaEncryptedEvent", "MediaError", "MediaKeyError", "MediaKeyMessageEvent", "MediaKeys", "MediaKeySession", "MediaKeyStatusMap", "MediaKeySystemAccess", "MediaList", "MediaMetadata", "MediaQueryList", "MediaQueryListEvent", "MediaRecorder", "MediaRecorderErrorEvent", "MediaSession", "MediaSource", "MediaSourceHandle", "MediaStream", "MediaStreamAudioDestinationNode", "MediaStreamAudioSourceNode", "MediaStreamEvent", "MediaStreamTrack", "MediaStreamTrackAudioSourceNode", "MediaStreamTrackAudioStats", "MediaStreamTrackEvent", "MediaStreamTrackGenerator", "MediaStreamTrackProcessor", "MediaStreamTrackVideoStats", "MessageChannel", "MessageEvent", "MessagePort", "MIDIAccess", "MIDIConnectionEvent", "MIDIInput", "MIDIInputMap", "MIDIMessageEvent", "MIDIOutput", "MIDIOutputMap", "MIDIPort", "MimeType", "MimeTypeArray", "ModelGenericSession", "Model<PERSON><PERSON><PERSON>", "MouseEvent", "MutationEvent", "MutationObserver", "MutationRecord", "NamedNodeMap", "NavigateEvent", "Navigation", "NavigationActivation", "NavigationCurrentEntryChangeEvent", "NavigationDestination", "NavigationHistoryEntry", "NavigationPreloadManager", "NavigationTransition", "Navigator", "Navigator<PERSON><PERSON><PERSON>", "NavigatorManagedData", "NavigatorUAData", "NetworkInformation", "Node", "Node<PERSON><PERSON><PERSON>", "NodeIterator", "NodeList", "Notification", "NotifyPaintEvent", "NotRestoredReasonDetails", "NotRestoredReasons", "Observable", "OfflineAudioCompletionEvent", "OfflineAudioContext", "OffscreenCanvas", "OffscreenCanvasRenderingContext2D", "Option", "OrientationSensor", "OscillatorNode", "OTPCredential", "OverconstrainedError", "PageRevealEvent", "PageSwapEvent", "PageTransitionEvent", "PannerNode", "PasswordCredential", "Path2D", "PaymentAddress", "PaymentManager", "PaymentMethodChangeEvent", "PaymentRequest", "PaymentRequestUpdateEvent", "PaymentResponse", "Performance", "PerformanceElementTiming", "PerformanceEntry", "PerformanceEventTiming", "PerformanceLongAnimationFrameTiming", "PerformanceLongTaskTiming", "PerformanceMark", "PerformanceMeasure", "PerformanceNavigation", "PerformanceNavigationTiming", "PerformanceObserver", "PerformanceObserverEntryList", "PerformancePaintTiming", "PerformanceResourceTiming", "PerformanceScriptTiming", "PerformanceServerTiming", "PerformanceTiming", "PeriodicSyncManager", "PeriodicWave", "Permissions", "PermissionStatus", "PERSISTENT", "PictureInPictureEvent", "PictureInPictureWindow", "Plugin", "PluginArray", "PointerEvent", "PopStateEvent", "Presentation", "PresentationAvailability", "PresentationConnection", "PresentationConnectionAvailableEvent", "PresentationConnectionCloseEvent", "PresentationConnectionList", "PresentationReceiver", "PresentationRequest", "PressureObserver", "PressureRecord", "ProcessingInstruction", "Profiler", "ProgressEvent", "PromiseRejectionEvent", "ProtectedAudience", "PublicKeyCredential", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PushSubscription", "PushSubscriptionOptions", "RadioNodeList", "Range", "ReadableByteStreamController", "ReadableStream", "ReadableStreamBYOBReader", "ReadableStreamBYOBRequest", "ReadableStreamDefaultController", "ReadableStreamDefaultReader", "RelativeOrientationSensor", "RemotePlayback", "ReportBody", "ReportingObserver", "Request", "ResizeObserver", "ResizeObserverEntry", "ResizeObserverSize", "Response", "RestrictionTarget", "RTCCertificate", "RTCDataChannel", "RTCDataChannelEvent", "RTCDtlsTransport", "RTCDTMFSender", "RTCDTMFToneChangeEvent", "RTCEncodedAudioFrame", "RTCEncodedVideoFrame", "RTCError", "RTCErrorEvent", "RTCIceCandidate", "RTCIceTransport", "RTCPeerConnection", "RTCPeerConnectionIceErrorEvent", "RTCPeerConnectionIceEvent", "RTCRtpReceiver", "RTCRtpScriptTransform", "RTCRtpSender", "RTCRtpTransceiver", "RTCSctpTransport", "RTCSessionDescription", "RTCStatsReport", "RTCTrackEvent", "Scheduler", "Scheduling", "Screen", "ScreenDetailed", "ScreenDetails", "ScreenOrientation", "ScriptProcessorNode", "ScrollTimeline", "SecurityPolicyViolationEvent", "Selection", "Sensor", "SensorErrorEvent", "Serial", "SerialPort", "ServiceWorker", "ServiceWorkerContainer", "ServiceWorkerRegistration", "ShadowRoot", "SharedStorage", "SharedStorageAppendMethod", "SharedStorageClearMethod", "SharedStorageDeleteMethod", "SharedStorageModifierMethod", "SharedStorageSetMethod", "SharedStorageWorklet", "SharedWorker", "SnapEvent", "SourceBuffer", "SourceBufferList", "SpeechSynthesis", "SpeechSynthesisErrorEvent", "SpeechSynthesisEvent", "SpeechSynthesisUtterance", "SpeechSynthesisVoice", "StaticRange", "StereoPannerNode", "Storage", "StorageBucket", "StorageBucketManager", "StorageEvent", "StorageManager", "StylePropertyMap", "StylePropertyMapReadOnly", "StyleSheet", "StyleSheetList", "SubmitEvent", "Subscriber", "SubtleCrypto", "SuppressedError", "SVGAElement", "SVGAngle", "SVGAnimatedAngle", "SVGAnimatedBoolean", "SVGAnimatedEnumeration", "SVGAnimatedInteger", "SVGAnimatedLength", "SVGAnimatedLengthList", "SVGAnimatedNumber", "SVGAnimatedNumberList", "SVGAnimatedPreserveAspectRatio", "SVGAnimatedRect", "SVGAnimatedString", "SVGAnimatedTransformList", "SVGAnimateElement", "SVGAnimateMotionElement", "SVGAnimateTransformElement", "SVGAnimationElement", "SVGCircleElement", "SVGClipPathElement", "SVGComponentTransferFunctionElement", "SVGDefsElement", "SVGDescElement", "SVGElement", "SVGEllipseElement", "SVGFEBlendElement", "SVGFEColorMatrixElement", "SVGFEComponentTransferElement", "SVGFECompositeElement", "SVGFEConvolveMatrixElement", "SVGFEDiffuseLightingElement", "SVGFEDisplacementMapElement", "SVGFEDistantLightElement", "SVGFEDropShadowElement", "SVGFEFloodElement", "SVGFEFuncAElement", "SVGFEFuncBElement", "SVGFEFuncGElement", "SVGFEFuncRElement", "SVGFEGaussianBlurElement", "SVGFEImageElement", "SVGFEMergeElement", "SVGFEMergeNodeElement", "SVGFEMorphologyElement", "SVGFEOffsetElement", "SVGFEPointLightElement", "SVGFESpecularLightingElement", "SVGFESpotLightElement", "SVGFETileElement", "SVGFETurbulenceElement", "SVGFilterElement", "SVGForeignObjectElement", "SVGGElement", "SVGGeometryElement", "SVGGradientElement", "SVGGraphicsElement", "SVGImageElement", "SVGLength", "SVGLengthList", "SVGLinearGradientElement", "SVGLineElement", "SVGMarkerElement", "SVGMaskElement", "SVGMatrix", "SVGMetadataElement", "SVGMPathElement", "SVGNumber", "SVGNumberList", "SVGPathElement", "SVGPatternElement", "SVGPoint", "SVGPointList", "SVGPolygonElement", "SVGPolylineElement", "SVGPreserveAspectRatio", "SVGRadialGradientElement", "SVGRect", "SVGRectElement", "SVGScriptElement", "SVGSetElement", "SVGStopElement", "SVGStringList", "SVGStyleElement", "SVGSVGElement", "SVGSwitchElement", "SVGSymbolElement", "SVGTextContentElement", "SVGTextElement", "SVGTextPathElement", "SVGTextPositioningElement", "SVGTitleElement", "SVGTransform", "SVGTransformList", "SVGTSpanElement", "SVGUnitTypes", "SVGUseElement", "SVGViewElement", "SyncManager", "TaskAttributionTiming", "TaskController", "TaskPriorityChangeEvent", "TaskSignal", "TEMPORARY", "Text", "TextDecoder", "TextDecoderStream", "TextEncoder", "TextEncoderStream", "TextEvent", "TextFormat", "TextFormatUpdateEvent", "TextMetrics", "TextTrack", "TextTrackCue", "TextTrackCueList", "TextTrackList", "TextUpdateEvent", "TimeEvent", "TimeRanges", "ToggleEvent", "Touch", "TouchEvent", "TouchList", "TrackEvent", "TransformStream", "TransformStreamDefaultController", "TransitionEvent", "<PERSON><PERSON><PERSON><PERSON>", "TrustedHTML", "TrustedScript", "TrustedScriptURL", "TrustedTypePolicy", "TrustedTypePolicyFactory", "UIEvent", "URL", "URLPattern", "URLSearchParams", "USB", "USBAlternateInterface", "USBConfiguration", "USBConnectionEvent", "USBDevice", "USBEndpoint", "USBInterface", "USBInTransferResult", "USBIsochronousInTransferPacket", "USBIsochronousInTransferResult", "USBIsochronousOutTransferPacket", "USBIsochronousOutTransferResult", "USBOutTransferResult", "UserActivation", "ValidityState", "VideoColorSpace", "VideoDecoder", "VideoEncoder", "VideoFrame", "VideoPlaybackQuality", "ViewTimeline", "ViewTransition", "ViewTransitionTypeSet", "VirtualKeyboard", "VirtualKeyboardGeometryChangeEvent", "VisibilityStateEntry", "VisualViewport", "VTTCue", "VTTRegion", "WakeLock", "WakeLockSentinel", "WaveShaperNode", "WebAssembly", "WebGL2RenderingContext", "WebGLActiveInfo", "WebGLBuffer", "WebGLContextEvent", "WebGLFramebuffer", "WebGLObject", "WebGLProgram", "WebGLQuery", "WebGLRenderbuffer", "WebGLRenderingContext", "WebGLSampler", "WebGLShader", "WebGLShaderPrecisionFormat", "WebGLSync", "WebGLTexture", "WebGLTransformFeedback", "WebGLUniformLocation", "WebGLVertexArrayObject", "WebSocket", "WebSocketError", "WebSocketStream", "WebTransport", "WebTransportBidirectionalStream", "WebTransportDatagramDuplexStream", "WebTransportError", "WebTransportReceiveStream", "WebTransportSendStream", "WGSLLanguageFeatures", "WheelEvent", "Window", "WindowControlsOverlay", "WindowControlsOverlayGeometryChangeEvent", "Worker", "Worklet", "WorkletGlobalScope", "WritableStream", "WritableStreamDefaultController", "WritableStreamDefaultWriter", "XMLDocument", "XMLHttpRequest", "XMLHttpRequestEventTarget", "XMLHttpRequestUpload", "XMLSerializer", "XPathEvaluator", "XPathExpression", "XPathResult", "XRAnchor", "XRAnchorSet", "XRBoundedReferenceSpace", "XRCamera", "XRCPUDepthInformation", "XRDepthInformation", "XRDOMOverlayState", "XRFrame", "XRHand", "XRHitTestResult", "XRHitTestSource", "XRInputSource", "XRInputSourceArray", "XRInputSourceEvent", "XRInputSourcesChangeEvent", "XRJointPose", "XRJointSpace", "<PERSON><PERSON><PERSON><PERSON>", "XRLightEstimate", "XRLightProbe", "<PERSON><PERSON><PERSON>", "XRRay", "XRReferenceSpace", "XRReferenceSpaceEvent", "XRRenderState", "XRRigidTransform", "XRSession", "XRSessionEvent", "XRSpace", "XRSystem", "XRTransientInputHitTestResult", "XRTransientInputHitTestSource", "XRView", "XRViewerPose", "XRViewport", "XRWebGLBinding", "XRWebGLDepthInformation", "XRWebGLLayer", "XSLTProcessor"]