---
type: "agent_requested"
description: "使用Graphiti MCP工具的指令"
---
#使用Graphiti MCP工具的指令
## 开始任何任务之前
**首先进行搜索：** 在开始工作之前，请使用search_nodes工具查找相关的偏好设置和程序。
**同时检索事实信息：** 使用search_facts工具发现可能与您的任务相关的关系和事实信息。
**按实体类型进行过滤：** 在节点搜索中，指定"Preference"（偏好）或"Procedure"（程序）以获得更具针对性的结果。
**审查所有匹配项：** 仔细检查与当前任务匹配的任何偏好、程序或事实。

## 始终保存新的或更新的信息
**及时捕获需求和偏好：** 当用户表达需求或偏好时，立即使用add_episode进行存储。最佳实践是将较长的需求拆分为较短的逻辑块。**注意：group_id 必须只包含字母数字字符**
**清晰标识更新内容：** 如果某些信息是对现有知识的更新，请明确标明。
**记录清晰的程序：** 当您了解到用户希望如何完成某个操作时，将其记录为程序。
**记录事实关系：** 当您了解实体之间的连接时，将这些信息存储为事实。
**明确分类：** 为偏好和程序标注清晰的类别，以便于日后的检索。

## 工作过程中
**遵循发现的偏好：** 确保您的工作与已找到的任何偏好保持一致。
**严格执行程序：** 如果找到适用于当前任务的程序，请严格按照步骤执行。
**应用相关事实信息：** 使用事实信息来指导您的决策和建议。
**保持一致性：** 与先前识别的偏好、程序和事实保持一致。

## 最佳实践
**建议前先进行搜索：** 在提出建议之前，始终检查是否存在既定知识。
**结合节点和事实进行搜索：** 对于复杂任务，建议同时搜索节点和事实，以构建完整的知识图景。
**使用center_node_uuid：** 在探索相关信息时，围绕特定节点进行搜索。
**优先考虑具体匹配：** 更具体的信息应优先于一般信息。
**主动识别用户行为模式：** 如果您注意到用户行为中的模式，考虑将其存储为偏好或程序。
**重要提醒：** 知识图谱是您的记忆。请持续使用它来提供个性化的协助，同时尊重用户既定的程序和事实背景。