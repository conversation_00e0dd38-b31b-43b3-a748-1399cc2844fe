{"name": "prompt-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "tsc", "start": "node dist/server/index.js", "dev": "ts-node src/server/index.ts", "lint": "eslint src --ext .ts,.tsx", "db:init": "node scripts/init-db.js", "db:backup": "node scripts/backup-db.js", "db:restore": "node scripts/restore-db.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/jest": "^30.0.0", "@types/node": "^24.0.14", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@types/uuid": "^9.0.7", "eslint": "^9.31.0", "jest": "^29.7.0", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "better-sqlite3": "^9.2.2", "dotenv": "^16.3.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "typescript-eslint": "^8.37.0", "uuid": "^9.0.1", "winston": "^3.17.0", "zod": "^4.0.5"}}