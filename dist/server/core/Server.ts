import express from 'express';
import { ProtocolHandler } from './ProtocolHandler';
import { RequestRouter } from './RequestRouter';

export class Server {
  private app: express.Application;
  private protocolHandler: ProtocolHandler;
  private requestRouter: RequestRouter;

  constructor() {
    this.app = express();
    this.protocolHandler = new ProtocolHandler();
    this.requestRouter = new RequestRouter(this.protocolHandler);

    this.app.use(express.json());
    this.app.post('/mcp', (req, res) => {
      try {
        const response = this.handleRequest(req.body);
        res.json(response);
      } catch (error: any) {
        res.status(400).json({ error: error.message });
      }
    });
  }

  start(port: number = 3000): void {
    this.app.listen(port, () => {
      console.log(`Server started on port ${port}`);
    });
  }

  handleRequest(request: any): any {
    return this.requestRouter.routeRequest(request);
  }
}