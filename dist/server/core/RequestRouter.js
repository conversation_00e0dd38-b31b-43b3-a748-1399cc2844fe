"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestRouter = void 0;
class RequestRouter {
    constructor(protocolHandler) {
        this.protocolHandler = protocolHandler;
    }
    routeRequest(request) {
        const { method, params } = request;
        switch (method) {
            case 'list-prompts':
                return this.handleListPrompts(params);
            case 'search-prompts':
                return this.handleSearchPrompts(params);
            default:
                throw new Error(`Unsupported method: ${method}`);
        }
    }
    handleListPrompts(params) {
        // TODO: Implement list prompts logic
        return {};
    }
    handleSearchPrompts(params) {
        // TODO: Implement search prompts logic
        return {};
    }
}
exports.RequestRouter = RequestRouter;
