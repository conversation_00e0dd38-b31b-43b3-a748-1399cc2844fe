"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Server = void 0;
const express_1 = __importDefault(require("express"));
const ProtocolHandler_1 = require("./ProtocolHandler");
const RequestRouter_1 = require("./RequestRouter");
class Server {
    constructor() {
        this.app = (0, express_1.default)();
        this.protocolHandler = new ProtocolHandler_1.ProtocolHandler();
        this.requestRouter = new RequestRouter_1.RequestRouter(this.protocolHandler);
        this.app.use(express_1.default.json());
        this.app.post('/mcp', (req, res) => {
            try {
                const response = this.handleRequest(req.body);
                res.json(response);
            }
            catch (error) {
                res.status(400).json({ error: error.message });
            }
        });
    }
    start(port = 3000) {
        this.app.listen(port, () => {
            console.log(`Server started on port ${port}`);
        });
    }
    handleRequest(request) {
        return this.requestRouter.routeRequest(request);
    }
}
exports.Server = Server;
